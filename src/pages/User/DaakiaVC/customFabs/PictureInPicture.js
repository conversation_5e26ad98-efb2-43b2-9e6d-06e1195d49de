import { useRef, useState, useCallback, useMemo, useEffect } from 'react';
import { createPortal } from 'react-dom';
import { Track } from 'livekit-client';
import { VideoTrack, useTracks, isTrackReference, useParticipants } from '@livekit/components-react';
import { AudioFilled, AudioMutedOutlined, VideoCameraFilled, VideoCameraOutlined, StopFilled, PhoneFilled } from '@ant-design/icons';
import { generateAvatar, parseMetadata } from '../utils/helper';
import { ScreenCaptureButton } from '../components/settings/ScreenCapture/ScreenCaptureButton';

// Speaking priority configuration
const SPEAKING_PRIORITY_CONFIG = {
  STICKY_DURATION: 5000, // Keep speaking participants visible for 5 seconds after they stop speaking
  UPDATE_INTERVAL: 500,   // Check for speaking changes every 500ms
};

// Responsive Grid PiP Content Component
function SimplePipContent({
  localParticipant,
  controlPosition = 'top',
  onDisconnect,
  room,
  setToastNotification,
  setToastStatus,
  setShowToast
}) {
  // Get all participants in the room (regardless of tracks)
  const allParticipants = useParticipants();

  // State for speaking priority tracking
  const [speakingHistory, setSpeakingHistory] = useState(new Map()); // participantId -> lastSpeakingTime

  // Get camera and screen share tracks for video display
  const allTracks = useTracks([
    { source: Track.Source.Camera, withPlaceholder: true },
    { source: Track.Source.ScreenShare, withPlaceholder: false },
  ]);

  // Find local camera track
  const localCameraTrack = allTracks
    .filter(isTrackReference)
    .find((track) =>
      track.participant.isLocal &&
      track.source === Track.Source.Camera
    );

  // Get screen share tracks (from any participant)
  const screenShareTracks = allTracks
    .filter(isTrackReference)
    .filter((track) => track.publication.source === Track.Source.ScreenShare);

  // Check if any screen sharing is active
  const isScreenShareActive = screenShareTracks.some((track) =>
    track.publication.isSubscribed && !track.publication.isMuted
  );

  // Get the active screen share track (prioritize the first subscribed one)
  const activeScreenShareTrack = screenShareTracks.find((track) =>
    track.publication.isSubscribed && !track.publication.isMuted
  );

  // Update speaking history when participants change
  useEffect(() => {
    const now = Date.now();
    const currentSpeaking = allParticipants.filter(p => p.isSpeaking);

    if (currentSpeaking.length > 0) {
      setSpeakingHistory(prev => {
        const updated = new Map(prev);
        currentSpeaking.forEach(p => {
          updated.set(p.identity, now);
        });
        return updated;
      });
    }
  }, [allParticipants.map(p => `${p.identity}-${p.isSpeaking}`).join(',')]);

  // Advanced Speaking Priority System with Sticky Behavior
  const getAdvancedSpeakingPriorityParticipants = useCallback((participants, maxCount) => {
    const now = Date.now();
    const currentSpeaking = participants.filter(p => p.isSpeaking);
    const currentSpeakingIds = new Set(currentSpeaking.map(p => p.identity));

    // Get recently speaking participants (within sticky duration)
    const recentlySpeaking = participants.filter(p => {
      const lastSpeakingTime = speakingHistory.get(p.identity);
      return lastSpeakingTime && (now - lastSpeakingTime) <= SPEAKING_PRIORITY_CONFIG.STICKY_DURATION;
    });

    // Priority order: Currently speaking > Recently speaking > Others
    const currentlySpakingParticipants = participants.filter(p => currentSpeakingIds.has(p.identity));
    const recentlySpokingParticipants = recentlySpeaking.filter(p => !currentSpeakingIds.has(p.identity));
    const otherParticipants = participants.filter(p =>
      !currentSpeakingIds.has(p.identity) &&
      !recentlySpeaking.some(rp => rp.identity === p.identity)
    );

    // Build final list with priority
    const prioritizedList = [
      ...currentlySpakingParticipants,
      ...recentlySpokingParticipants,
      ...otherParticipants
    ];

    // Return up to maxCount participants
    return prioritizedList.slice(0, maxCount);
  }, [speakingHistory]);

  // Periodic update for speaking priority (cleanup old speaking history)
  useEffect(() => {
    const interval = setInterval(() => {
      const now = Date.now();
      setSpeakingHistory(prev => {
        const updated = new Map();
        prev.forEach((lastSpeakingTime, participantId) => {
          // Keep only recent speaking history
          if ((now - lastSpeakingTime) <= SPEAKING_PRIORITY_CONFIG.STICKY_DURATION * 2) {
            updated.set(participantId, lastSpeakingTime);
          }
        });
        return updated;
      });
    }, SPEAKING_PRIORITY_CONFIG.UPDATE_INTERVAL);

    return () => clearInterval(interval);
  }, [setSpeakingHistory]);

  // Get remote participants with advanced speaking priority
  // When screen sharing: max 2 remotes, when no screen share: max 3 remotes
  const maxRemotes = isScreenShareActive ? 2 : 3;
  const allRemoteParticipants = allParticipants.filter((participant) => !participant.isLocal);
  const remoteParticipants = getAdvancedSpeakingPriorityParticipants(allRemoteParticipants, maxRemotes);

  // Create remote participant data with their camera tracks (if any)
  const remoteParticipantsWithTracks = remoteParticipants.map((participant) => {
    const cameraTrack = allTracks
      .filter(isTrackReference)
      .find((track) =>
        track.participant.identity === participant.identity &&
        track.source === Track.Source.Camera
      );

    return {
      participant,
      track: cameraTrack || null
    };
  });

  // Check if local video is enabled (camera on)
  const isVideoEnabled = localCameraTrack?.publication &&
    !localCameraTrack.publication.isMuted &&
    localCameraTrack.publication.isSubscribed &&
    localCameraTrack.publication.kind === "video";

  // Check if local participant is speaking
  const isSpeaking = localParticipant?.isSpeaking || false;

  // Get participant count for dynamic grid (now based on all remote participants)
  const participantCount = remoteParticipants.length;

  // Dynamic grid class based on participant count, screen share status, and control position
  const getGridClass = (count, controlPos, hasScreenShare) => {
    const baseClass = controlPos === 'top' ? 'control-top' : 'control-bottom';
    const screenSharePrefix = hasScreenShare ? 'pip-grid-screenshare' : 'pip-grid';

    if (hasScreenShare) {
      // WITH SCREEN SHARE layouts
      switch(count) {
        case 0: return `${screenSharePrefix}-solo ${baseClass}`;      // Screen share + local + control
        case 1: return `${screenSharePrefix}-two ${baseClass}`;       // Screen share + local + 1 remote + control
        case 2: return `${screenSharePrefix}-three ${baseClass}`;     // Screen share + local + 2 remotes + control
        default: return `${screenSharePrefix}-three ${baseClass}`;    // Max: screen share + local + 2 remotes + control
      }
    } else {
      // NO SCREEN SHARE layouts (your existing perfect logic)
      switch(count) {
        case 0: return `${screenSharePrefix}-solo ${baseClass}`;      // Just local + control
        case 1: return `${screenSharePrefix}-two ${baseClass}`;       // Local + 1 remote + control
        case 2: return `${screenSharePrefix}-three ${baseClass}`;     // Local + 2 remote + control
        case 3: return `${screenSharePrefix}-four ${baseClass}`;      // Local + 3 remote + control
        default: return `${screenSharePrefix}-four ${baseClass}`;     // Max 4 total + control
      }
    }
  };

  // Get avatar info for local participant
  const avatarName = localParticipant?.name
    ? generateAvatar(localParticipant.name)
    : generateAvatar(localParticipant.identity);
  let avatarColor = '#7C4DFF';
  try {
    const metaColor = parseMetadata(localParticipant?.metadata)?.color;
    if (metaColor) avatarColor = metaColor;
  } catch (e) { /* ignore */ }

  // Helper function to get remote participant avatar info
  const getRemoteParticipantInfo = (participant) => {
    const name = participant?.name
      ? generateAvatar(participant.name)
      : generateAvatar(participant.identity);
    let color = '#7C4DFF';
    try {
      const metaColor = parseMetadata(participant?.metadata)?.color;
      if (metaColor) color = metaColor;
    } catch (e) { /* ignore */ }
    return { name, color };
  };

  // Control tile component
  function ControlTile() {
    const toggleMic = useCallback(() => {
      localParticipant.setMicrophoneEnabled(!localParticipant.isMicrophoneEnabled);
    }, [localParticipant]);

    const toggleVideo = useCallback(() => {
      localParticipant.setCameraEnabled(!localParticipant.isCameraEnabled);
    }, [localParticipant]);

    // Check if screen sharing is active
    const isScreenSharing = useMemo(() => {
      return localParticipant.getTrackPublications().some(
        (pub) => pub.source === Track.Source.ScreenShare && !pub.isMuted
      );
    }, [localParticipant]);

    // Handle stopping screen share
    const handleStopScreenShare = useCallback(async () => {
      try {
        const screenTrack = localParticipant.getTrackPublications().find(
          (pub) => pub.source === Track.Source.ScreenShare
        );
        if (screenTrack) {
          await screenTrack.track.stop();
          localParticipant.unpublishTrack(screenTrack.track);
        }
      } catch (error) {
        console.error('Error stopping screen share:', error);
      }
    }, [localParticipant]);

    return (
      <div className="pip-tile pip-control-tile">
        <div className="pip-control-content">
          <div className="pip-control-placeholder">
            <button 
              className={`pip-control-dot ${localParticipant.isMicrophoneEnabled ? 'active' : ''}`}
              onClick={toggleMic}
            >
              {localParticipant.isMicrophoneEnabled ? (
                <AudioFilled style={{ color: '#fff' }} />
              ) : (
                <AudioMutedOutlined style={{ color: '#666' }} />
              )}
            </button>
            <button 
              className={`pip-control-dot ${localParticipant.isCameraEnabled ? 'active' : ''}`}
              onClick={toggleVideo}
            >
              {localParticipant.isCameraEnabled ? (
                <VideoCameraFilled style={{ color: '#fff' }} />
              ) : (
                <VideoCameraOutlined style={{ color: '#666' }} />
              )}
            </button>
            {isScreenSharing && (
              <button 
                className="pip-control-dot screen-share"
                onClick={handleStopScreenShare}
              >
                <StopFilled style={{ color: '#fff' }} />
              </button>
            )}
            <button 
              className="pip-control-dot disconnect"
              onClick={onDisconnect}
            >
              <PhoneFilled style={{ color: '#fff' }} />
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="pip-main-container">
      {/* Grid container for tiles */}
      <div className={`pip-grid-container ${getGridClass(participantCount, controlPosition, isScreenShareActive)}`}>
        {/* Control tile - positioned based on controlPosition */}
        {controlPosition === 'top' && (
          <ControlTile />
        )}

        {/* MAIN LAYOUT LOGIC: Screen Share vs No Screen Share */}
        {isScreenShareActive ? (
          // WITH SCREEN SHARE: Screen share tile on top, participants below
          <>
            {/* Screen Share Tile - Always the big tile on top */}
            <div className="pip-tile pip-tile-screenshare">
              {activeScreenShareTrack ? (
                <div className="pip-video-container">
                  <VideoTrack
                    trackRef={activeScreenShareTrack}
                    style={{
                      width: '100%',
                      height: '100%',
                      objectFit: 'contain',
                      borderRadius: '2vmin'
                    }}
                  />
                  {/* Floating Screen Capture Button */}
                  <div className="pip-screen-capture-button">
                    <ScreenCaptureButton
                      room={room}
                      screenShareTracks={screenShareTracks}
                      focusTrack={activeScreenShareTrack}
                      setToastNotification={setToastNotification}
                      setToastStatus={setToastStatus}
                      setShowToast={setShowToast}
                      setShowPopover={() => {}} // No popover in PiP
                    />
                  </div>
                </div>
              ) : (
                <div className="pip-tile-center-dot" />
              )}
            </div>

            {/* Local Participant Tile - Below screen share */}
            {/* MEMORY OPTIMIZATION: When screen sharing, show only avatar for local participant (no video) */}
            <div className={`pip-tile pip-tile-small pip-tile-local ${isSpeaking ? 'pip-tile-speaking' : ''}`}>
              {/* Always show avatar for local participant when screen sharing (memory optimization) */}
              <div className="pip-tile-avatar-center">
                <div
                  className="pip-tile-avatar pip-tile-avatar-small"
                  style={{ backgroundColor: avatarColor }}
                >
                  {avatarName}
                </div>
              </div>

              {/* Corner accent lights for local speaking */}
              {isSpeaking && (
                <>
                  <div style={{
                    position: 'absolute',
                    bottom: '0.5vmin',
                    left: '0.5vmin',
                    width: '1vmin',
                    height: '1vmin',
                    background: '#1e8cfa',
                    borderRadius: '50%',
                    zIndex: 2,
                    animation: 'pip-corner-pulse 1.8s cubic-bezier(0.4, 0, 0.6, 1) infinite',
                    animationDelay: '0.6s'
                  }} />
                  <div style={{
                    position: 'absolute',
                    bottom: '0.5vmin',
                    right: '0.5vmin',
                    width: '1vmin',
                    height: '1vmin',
                    background: '#1e8cfa',
                    borderRadius: '50%',
                    zIndex: 2,
                    animation: 'pip-corner-pulse 1.8s cubic-bezier(0.4, 0, 0.6, 1) infinite',
                    animationDelay: '0.9s'
                  }} />
                </>
              )}
            </div>
          </>
        ) : (
          // NO SCREEN SHARE: Your existing perfect logic - Local participant as main tile
          <div className={`pip-tile pip-tile-main ${isSpeaking ? 'pip-tile-speaking' : ''}`}>
            {isVideoEnabled && localCameraTrack ? (
              // Show video when camera is on
              <div className="pip-video-container">
                <VideoTrack
                  trackRef={localCameraTrack}
                  style={{
                    width: '100%',
                    height: '100%',
                    objectFit: 'contain',
                    borderRadius: '2vmin'
                  }}
                />
              </div>
            ) : (
              // Show avatar when camera is off
              <div className="pip-tile-avatar-center">
                <div
                  className="pip-tile-avatar"
                  style={{ backgroundColor: avatarColor }}
                >
                  {avatarName}
                </div>
              </div>
            )}

            {/* Corner accent lights for local speaking */}
            {isSpeaking && (
              <>
                <div style={{
                  position: 'absolute',
                  bottom: '1vmin',
                  left: '1vmin',
                  width: '1.5vmin',
                  height: '1.5vmin',
                  background: '#1e8cfa',
                  borderRadius: '50%',
                  zIndex: 2,
                  animation: 'pip-corner-pulse 1.8s cubic-bezier(0.4, 0, 0.6, 1) infinite',
                  animationDelay: '0.6s'
                }} />
                <div style={{
                  position: 'absolute',
                  bottom: '1vmin',
                  right: '1vmin',
                  width: '1.5vmin',
                  height: '1.5vmin',
                  background: '#1e8cfa',
                  borderRadius: '50%',
                  zIndex: 2,
                  animation: 'pip-corner-pulse 1.8s cubic-bezier(0.4, 0, 0.6, 1) infinite',
                  animationDelay: '0.9s'
                }} />
              </>
            )}
          </div>
        )}

        {/* Dynamic remote participant tiles - Only show when NO screen share */}
        {!isScreenShareActive && remoteParticipantsWithTracks.map((participantData, index) => {
          const { participant, track } = participantData;
          const remoteInfo = getRemoteParticipantInfo(participant);
          const isRemoteSpeaking = participant?.isSpeaking || false;

          // Check if remote video is enabled (only if they have a track)
          const isRemoteVideoEnabled = track?.publication &&
            !track.publication.isMuted &&
            track.publication.isSubscribed &&
            track.publication.kind === "video";

          return (
            <div
              key={participant.identity || `remote-${index}`}
              className={`pip-tile pip-tile-small pip-tile-remote ${isRemoteSpeaking ? 'pip-tile-speaking' : ''}`}
            >
              {isRemoteVideoEnabled && track ? (
                // Show remote video
                <div className="pip-video-container">
                  <VideoTrack
                    trackRef={track}
                    style={{
                      width: '100%',
                      height: '100%',
                      objectFit: 'contain',
                      borderRadius: '2vmin'
                    }}
                  />
                </div>
              ) : (
                // Show remote avatar (always show avatar when no video or camera off)
                <div className="pip-tile-avatar-center">
                  <div
                    className="pip-tile-avatar pip-tile-avatar-small"
                    style={{ backgroundColor: remoteInfo.color }}
                  >
                    {remoteInfo.name}
                  </div>
                </div>
              )}

              {/* Corner accent lights for remote speaking */}
              {isRemoteSpeaking && (
                <>
                  <div style={{
                    position: 'absolute',
                    bottom: '0.5vmin',
                    left: '0.5vmin',
                    width: '1vmin',
                    height: '1vmin',
                    background: '#1e8cfa',
                    borderRadius: '50%',
                    zIndex: 2,
                    animation: 'pip-corner-pulse 1.8s cubic-bezier(0.4, 0, 0.6, 1) infinite',
                    animationDelay: '0.6s'
                  }} />
                  <div style={{
                    position: 'absolute',
                    bottom: '0.5vmin',
                    right: '0.5vmin',
                    width: '1vmin',
                    height: '1vmin',
                    background: '#1e8cfa',
                    borderRadius: '50%',
                    zIndex: 2,
                    animation: 'pip-corner-pulse 1.8s cubic-bezier(0.4, 0, 0.6, 1) infinite',
                    animationDelay: '0.9s'
                  }} />
                </>
              )}
            </div>
          );
        })}

        {/* Dynamic remote participant tiles - Only show when WITH screen share */}
        {/* MEMORY OPTIMIZATION: When screen sharing, show only avatars for remote participants */}
        {isScreenShareActive && remoteParticipantsWithTracks.map((participantData, index) => {
          const { participant } = participantData;
          const remoteInfo = getRemoteParticipantInfo(participant);
          const isRemoteSpeaking = participant?.isSpeaking || false;

          return (
            <div
              key={participant.identity || `remote-screenshare-${index}`}
              className={`pip-tile pip-tile-small pip-tile-remote ${isRemoteSpeaking ? 'pip-tile-speaking' : ''}`}
            >
              {/* Always show avatar for remote participants when screen sharing (memory optimization) */}
              <div className="pip-tile-avatar-center">
                <div
                  className="pip-tile-avatar pip-tile-avatar-small"
                  style={{ backgroundColor: remoteInfo.color }}
                >
                  {remoteInfo.name}
                </div>
              </div>

              {/* Corner accent lights for remote speaking */}
              {isRemoteSpeaking && (
                <>
                  <div style={{
                    position: 'absolute',
                    bottom: '0.5vmin',
                    left: '0.5vmin',
                    width: '1vmin',
                    height: '1vmin',
                    background: '#1e8cfa',
                    borderRadius: '50%',
                    zIndex: 2,
                    animation: 'pip-corner-pulse 1.8s cubic-bezier(0.4, 0, 0.6, 1) infinite',
                    animationDelay: '0.6s'
                  }} />
                  <div style={{
                    position: 'absolute',
                    bottom: '0.5vmin',
                    right: '0.5vmin',
                    width: '1vmin',
                    height: '1vmin',
                    background: '#1e8cfa',
                    borderRadius: '50%',
                    zIndex: 2,
                    animation: 'pip-corner-pulse 1.8s cubic-bezier(0.4, 0, 0.6, 1) infinite',
                    animationDelay: '0.9s'
                  }} />
                </>
              )}
            </div>
          );
        })}

        {/* Control tile - positioned at bottom if controlPosition is 'bottom' */}
        {controlPosition === 'bottom' && <ControlTile />}
      </div>
    </div>
  );
}

export function usePictureInPicture({
  setIsPIPEnabled,
  setToastNotification,
  setToastStatus,
  setShowToast,
  localParticipant,
  controlPosition = 'top'
}) {
  const pipWindowRef = useRef(null);
  const pipContainerRef = useRef(null);
  const [pipWindowDocument, setPipWindowDocument] = useState(null);

  // Get participant count for dynamic sizing
  const allTracks = useTracks([
    { source: Track.Source.Camera, withPlaceholder: true },
  ]);
  const remoteTracks = allTracks
    .filter(isTrackReference)
    .filter((track) => !track.participant.isLocal);
  const participantCount = remoteTracks.length;

  // Dynamic PiP window configuration based on participant count
  const getDynamicConfig = (count) => {
    switch(count) {
      case 0: // Solo - minimum size
        return { width: 180, height: 297 };  // Increased from 270
      case 1: // Two participants - small window
        return { width: 220, height: 363 };  // Increased from 330
      case 2: // Three participants - medium window
        return { width: 280, height: 429 };  // Increased from 390
      case 3: // Four participants - larger window
        return { width: 320, height: 495 };  // Increased from 450
      default:
        return { width: 320, height: 528 };  // Increased from 480
    }
  };

  const defaultConfig = useMemo(() => getDynamicConfig(participantCount), [participantCount]);

  // Track previous participant count for notifications
  const prevParticipantCountRef = useRef(participantCount);

  // Auto-resize PiP window when participant count changes
  useEffect(() => {
    if (pipWindowRef.current && pipWindowDocument) {
      const newConfig = getDynamicConfig(participantCount);
      const prevCount = prevParticipantCountRef.current;

      try {
        // Resize the existing PiP window
        pipWindowRef.current.resizeTo(newConfig.width, newConfig.height);

        // Show notification about participant change (only if PiP is active)
        if (prevCount !== participantCount && prevCount !== undefined) {
          const participantChange = participantCount > prevCount ? 'joined' : 'left';
          const totalParticipants = participantCount + 1; // +1 for local participant

          setToastNotification(`Participant ${participantChange}. PiP adjusted for ${totalParticipants} participant${totalParticipants > 1 ? 's' : ''}.`);
          setToastStatus("info");
          setShowToast(true);
        }

      } catch (error) {
        console.log('PiP resize not supported on this browser');
      }
    }

    // Update previous count
    prevParticipantCountRef.current = participantCount;
  }, [participantCount, pipWindowDocument, setToastNotification, setToastStatus, setShowToast]);

  // Check Document PiP support
  const isSupported = useMemo(() => {
    return 'documentPictureInPicture' in window;
  }, []);

  // Close PiP window
  const closePipWindow = useCallback(() => {
    if (pipWindowRef.current) {
      pipWindowRef.current.close();
      pipWindowRef.current = null;
    }
    setPipWindowDocument(null);
    pipContainerRef.current = null;
    setIsPIPEnabled(false);
  }, [setIsPIPEnabled]);

  // Handle disconnect from PiP
  const handleDisconnect = useCallback(() => {
    if (localParticipant) {
      localParticipant.disconnect();
    }
    closePipWindow();
  }, [localParticipant, closePipWindow]);

  // Simple PiP Content
  const PipContent = useCallback(() => {
    return <SimplePipContent
      localParticipant={localParticipant}
      controlPosition={controlPosition}
      onDisconnect={handleDisconnect}
      room={localParticipant?.room}
      setToastNotification={setToastNotification}
      setToastStatus={setToastStatus}
      setShowToast={setShowToast}
    />;
  }, [localParticipant, controlPosition, handleDisconnect, setToastNotification, setToastStatus, setShowToast]);

  // Responsive Grid styles
  const getPipStyles = useCallback(() => `
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      background: #000;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      overflow: hidden;
      width: 100vw;
      height: 100vh;
      color: white;
    }

    .pip-container {
      width: 100vw;
      height: 100vh;
      background: #000;
      padding: 2vmin;
      display: flex;
      align-items: center;
      justify-content: center;
      box-sizing: border-box;
    }

    .pip-main-container {
      width: 100%;
      height: 100%;
      display: flex;
      flex-direction: column;
    }

    .pip-grid-container {
      width: 100%;
      height: 100%;
      display: grid;
      gap: 1vmin;
      box-sizing: border-box;
    }

    /* Dynamic grid layouts based on participant count */
    /* Solo layouts (1 participant + control) */
    .pip-grid-solo.control-top {
      grid-template-columns: 1fr;
      grid-template-rows: auto 1fr;
    }

    .pip-grid-solo.control-bottom {
      grid-template-columns: 1fr;
      grid-template-rows: 1fr auto;
    }

    /* Two participant layouts (2 participants + control) - Vertical stack */
    .pip-grid-two.control-top {
      grid-template-columns: 1fr;
      grid-template-rows: auto 1fr 1fr;
    }

    .pip-grid-two.control-bottom {
      grid-template-columns: 1fr;
      grid-template-rows: 1fr 1fr auto;
    }

    /* Three participant layouts (3 participants + control) - 1 large top, 2 small bottom */
    .pip-grid-three.control-top {
      grid-template-columns: 1fr 1fr;
      grid-template-rows: auto 2fr 1fr;
    }

    .pip-grid-three.control-bottom {
      grid-template-columns: 1fr 1fr;
      grid-template-rows: 2fr 1fr auto;
    }

    /* Four participant layouts (4 participants + control) - 1 large top, 3 small bottom */
    .pip-grid-four.control-top {
      grid-template-columns: 1fr 1fr 1fr;
      grid-template-rows: auto 2fr 1fr;
    }

    .pip-grid-four.control-bottom {
      grid-template-columns: 1fr 1fr 1fr;
      grid-template-rows: 2fr 1fr auto;
    }

    /* SCREEN SHARE LAYOUTS */
    /* Screen share solo layouts (screen share + local + control) - Equal sized tiles */
    .pip-grid-screenshare-solo.control-top {
      grid-template-columns: 1fr;
      grid-template-rows: auto 1fr 1fr;
    }

    .pip-grid-screenshare-solo.control-bottom {
      grid-template-columns: 1fr;
      grid-template-rows: 1fr 1fr auto;
    }

    /* Screen share two layouts (screen share + local + 1 remote + control) */
    .pip-grid-screenshare-two.control-top {
      grid-template-columns: 1fr 1fr;
      grid-template-rows: auto 2fr 1fr;
    }

    .pip-grid-screenshare-two.control-bottom {
      grid-template-columns: 1fr 1fr;
      grid-template-rows: 2fr 1fr auto;
    }

    /* Screen share three layouts (screen share + local + 2 remotes + control) */
    .pip-grid-screenshare-three.control-top {
      grid-template-columns: 1fr 1fr 1fr;
      grid-template-rows: auto 2fr 1fr;
    }

    .pip-grid-screenshare-three.control-bottom {
      grid-template-columns: 1fr 1fr 1fr;
      grid-template-rows: 2fr 1fr auto;
    }

    .pip-tile {
      border: none;
      border-radius: 2vmin;
      position: relative;
      overflow: hidden;
      display: flex;
      align-items: center;
      justify-content: center;
      box-sizing: border-box;
      width: 100%;
      height: 100%;
      transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
    }

    /* Control tile styles */
    .pip-control-tile {
      background-color: rgba(42, 42, 42, 0.95);
      backdrop-filter: blur(10px);
      border-radius: 2.5vmin;
      display: flex;
      align-items: center;
      justify-content: center;
      min-height: 16vmin;
      height: 16vmin;
      grid-column: 1 / -1;
      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
      padding: 1.5vmin;
      overflow: hidden;
      border: 1px solid rgba(255, 255, 255, 0.1);
    }

    .pip-control-content {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 100%;
      height: 100%;
      padding: 0;
    }

    .pip-control-placeholder {
      display: flex;
      gap: 2.5vmin;
      align-items: center;
      height: 100%;
      padding: 0.5vmin;
    }

    .pip-control-dot {
      width: 14vmin;
      height: 8vmin;
      background-color: rgba(255, 255, 255, 0.1);
      border-radius: 1.2vmin;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      cursor: pointer;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      margin: 0;
      padding: 1vmin;
      display: flex;
      align-items: center;
      justify-content: center;
      border: 1px solid rgba(255, 255, 255, 0.1);
      outline: none;
      position: relative;
      overflow: hidden;
    }

    .pip-control-dot::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(145deg, rgba(255,255,255,0.1), rgba(255,255,255,0));
      border-radius: 1.2vmin;
      pointer-events: none;
      opacity: 0;
      transition: opacity 0.3s ease;
    }

    .pip-control-dot:hover::before {
      opacity: 1;
    }

    .pip-control-dot svg {
      width: 11.4px;
      height: 11.4px;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      color: rgba(255, 255, 255, 0.8);
    }

    .pip-control-dot:hover {
      transform: translateY(-1px);
      box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2);
      background-color: rgba(255, 255, 255, 0.15);
    }

    .pip-control-dot:hover svg {
      transform: scale(1.1);
      color: #fff;
    }

    .pip-control-dot:active {
      transform: translateY(0);
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
    }

    .pip-control-dot.active {
      background-color: #1e8cfa;
      box-shadow: 0 4px 12px rgba(30, 140, 250, 0.3);
      border-color: rgba(255, 255, 255, 0.2);
    }

    .pip-control-dot.active svg {
      color: #fff;
    }

    .pip-control-dot.disconnect {
      background-color: rgba(255, 59, 48, 0.9);
      box-shadow: 0 4px 12px rgba(255, 59, 48, 0.3);
      border-color: rgba(255, 255, 255, 0.2);
    }

    .pip-control-dot.disconnect:hover {
      background-color: #ff3b30;
      box-shadow: 0 6px 16px rgba(255, 59, 48, 0.4);
    }

    .pip-control-dot.disconnect svg {
      color: #fff;
    }

    .pip-control-dot.disconnect:active {
      background-color: #ff1a1a;
      box-shadow: 0 2px 8px rgba(255, 59, 48, 0.3);
    }

    /* Main tile positioning based on layout */
    .pip-tile-main {
      background-color: #1e1e1e;
      border-radius: 2.5vmin;
      transition: box-shadow 0.3s ease, border 0.3s ease;
    }

    /* Screen share tile styles */
    .pip-tile-screenshare {
      background-color: #1e1e1e;
      border-radius: 2.5vmin;
      transition: box-shadow 0.3s ease, border 0.3s ease;
      grid-column: 1 / -1;
      grid-row: 2;
    }

    /* Screen share tile positioning for different layouts */
    .pip-grid-screenshare-solo.control-top .pip-tile-screenshare,
    .pip-grid-screenshare-two.control-top .pip-tile-screenshare,
    .pip-grid-screenshare-three.control-top .pip-tile-screenshare {
      grid-column: 1 / -1;
      grid-row: 2;
    }

    .pip-grid-screenshare-solo.control-bottom .pip-tile-screenshare,
    .pip-grid-screenshare-two.control-bottom .pip-tile-screenshare,
    .pip-grid-screenshare-three.control-bottom .pip-tile-screenshare {
      grid-column: 1 / -1;
      grid-row: 1;
    }

    /* Local participant tile when screen sharing */
    .pip-tile-local {
      background-color: #1e1e1e;
      border-radius: 2vmin;
      transition: box-shadow 0.3s ease, border 0.3s ease;
    }

    /* Main tile positioning for different layouts */
    .pip-grid-solo.control-top .pip-tile-main {
      grid-column: 1;
      grid-row: 2;
    }

    .pip-grid-solo.control-bottom .pip-tile-main {
      grid-column: 1;
      grid-row: 1;
    }

    .pip-grid-two.control-top .pip-tile-main {
      grid-column: 1;
      grid-row: 2;
    }

    .pip-grid-two.control-bottom .pip-tile-main {
      grid-column: 1;
      grid-row: 1;
    }

    .pip-grid-three.control-top .pip-tile-main,
    .pip-grid-four.control-top .pip-tile-main {
      grid-column: 1 / -1;
      grid-row: 2;
    }

    .pip-grid-three.control-bottom .pip-tile-main,
    .pip-grid-four.control-bottom .pip-tile-main {
      grid-column: 1 / -1;
      grid-row: 1;
    }

    .pip-tile-speaking {
      box-shadow: 0 0 0 2px #1e8cfa, 0 0 12px rgba(30, 140, 250, 0.3) !important;
      animation: pip-speaking-pulse 3s cubic-bezier(0.4, 0, 0.2, 1) infinite;
      position: relative;
      transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
      will-change: transform, box-shadow;
    }

    .pip-tile-speaking::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      border-radius: 2.5vmin;
      background: radial-gradient(circle at center, rgba(30, 140, 250, 0.2) 0%, transparent 70%);
      animation: pip-speaking-glow 3s ease-in-out infinite;
      z-index: 0;
      transition: opacity 0.5s cubic-bezier(0.4, 0, 0.2, 1);
      will-change: opacity, transform;
    }

    .pip-tile-speaking::after {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      border-radius: 2.5vmin;
      background: radial-gradient(circle at center, rgba(30, 140, 250, 0.1) 0%, transparent 60%);
      animation: pip-speaking-delay 3s ease-in-out infinite;
      z-index: 0;
      opacity: 0;
      transition: opacity 0.5s cubic-bezier(0.4, 0, 0.2, 1);
      will-change: opacity, transform;
    }

    @keyframes pip-speaking-pulse {
      0% {
        box-shadow: 0 0 0 2px #1e8cfa, 0 0 12px rgba(30, 140, 250, 0.3);
        transform: scale(1);
        opacity: 1;
      }
      15% {
        box-shadow: 0 0 0 2.5px #1e8cfa, 0 0 16px rgba(30, 140, 250, 0.4);
        transform: scale(1.003);
        opacity: 1;
      }
      30% {
        box-shadow: 0 0 0 3px #1e8cfa, 0 0 20px rgba(30, 140, 250, 0.5);
        transform: scale(1.006);
        opacity: 1;
      }
      45% {
        box-shadow: 0 0 0 2.5px #1e8cfa, 0 0 16px rgba(30, 140, 250, 0.4);
        transform: scale(1.003);
        opacity: 1;
      }
      60% {
        box-shadow: 0 0 0 2px #1e8cfa, 0 0 12px rgba(30, 140, 250, 0.3);
        transform: scale(1);
        opacity: 0.95;
      }
      100% {
        box-shadow: 0 0 0 2px #1e8cfa, 0 0 12px rgba(30, 140, 250, 0.3);
        transform: scale(1);
        opacity: 1;
      }
    }

    @keyframes pip-speaking-glow {
      0% {
        opacity: 0.15;
        transform: scale(0.98);
      }
      30% {
        opacity: 0.4;
        transform: scale(1.02);
      }
      60% {
        opacity: 0.15;
        transform: scale(0.98);
      }
      100% {
        opacity: 0.15;
        transform: scale(0.98);
      }
    }

    @keyframes pip-speaking-delay {
      0% {
        opacity: 0;
        transform: scale(0.95);
      }
      20% {
        opacity: 0;
        transform: scale(0.95);
      }
      40% {
        opacity: 0.3;
        transform: scale(1.03);
      }
      60% {
        opacity: 0;
        transform: scale(0.95);
      }
      100% {
        opacity: 0;
        transform: scale(0.95);
      }
    }

    /* Small tile positioning for different layouts */
    .pip-tile-small {
      aspect-ratio: 16 / 9;
      border-radius: 2vmin;
      background-color: #1e1e1e;
      transition: box-shadow 0.3s ease, border 0.3s ease;
    }

    /* Two participant layout - second participant positioning */
    .pip-grid-two.control-top .pip-tile-small {
      grid-row: 3;
      grid-column: 1;
    }

    .pip-grid-two.control-bottom .pip-tile-small {
      grid-row: 2;
      grid-column: 1;
    }

    /* Three participant layout - remote participants positioning */
    .pip-grid-three.control-top .pip-tile-small {
      grid-row: 3;
    }

    .pip-grid-three.control-bottom .pip-tile-small {
      grid-row: 2;
    }

    /* Four participant layout - remote participants positioning */
    .pip-grid-four.control-top .pip-tile-small {
      grid-row: 3;
    }

    .pip-grid-four.control-bottom .pip-tile-small {
      grid-row: 2;
    }

    /* Screen share layouts - small tile positioning */
    /* Screen share solo layout - local participant positioning */
    .pip-grid-screenshare-solo.control-top .pip-tile-small {
      grid-row: 3;
      grid-column: 1;
    }

    .pip-grid-screenshare-solo.control-bottom .pip-tile-small {
      grid-row: 2;
      grid-column: 1;
    }

    /* Screen share two layout - local + 1 remote positioning */
    .pip-grid-screenshare-two.control-top .pip-tile-small {
      grid-row: 3;
    }

    .pip-grid-screenshare-two.control-bottom .pip-tile-small {
      grid-row: 2;
    }

    /* Screen share three layout - local + 2 remotes positioning */
    .pip-grid-screenshare-three.control-top .pip-tile-small {
      grid-row: 3;
    }

    .pip-grid-screenshare-three.control-bottom .pip-tile-small {
      grid-row: 2;
    }

    .pip-tile-1 {
      background-color: #1e1e1e;
      border-radius: 2vmin;
    }

    .pip-tile-2 {
      background-color: #1e1e1e;
      border-radius: 2vmin;
    }

    .pip-tile-3 {
      background-color: #1e1e1e;
      border-radius: 2vmin;
    }

    .pip-tile-center-dot {
      width: 2vmin;
      height: 2vmin;
      background-color: #ff69b4;
      border-radius: 50%;
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
    }

    .pip-video-container {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      overflow: hidden;
      border-radius: 2vmin;
    }

    .pip-video-container video {
      width: 100% !important;
      height: 100% !important;
      object-fit: contain !important;
      aspect-ratio: 16 / 9 !important;
      max-width: 100%;
      max-height: 100%;
      border-radius: 2vmin !important;
    }

    .pip-tile-pip-custom .lk-participant-metadata {
      display: none !important;
    }
    .pip-tile-participant-wrapper {
      width: 100%;
      height: 100%;
      position: relative;
    }
    .pip-tile-overlay {
      position: absolute;
      left: 0;
      right: 0;
      bottom: 0;
      width: 100%;
      display: flex;
      justify-content: space-between;
      align-items: flex-end;
      pointer-events: none;
      padding: 0.25em 0.4em 0.35em 0.4em;
      box-sizing: border-box;
    }
    .pip-tile-overlay-left {
      display: flex;
      align-items: center;
      gap: 0.35em;
      background: rgba(20,20,20,0.72);
      border-radius: 0.35em;
      padding: 0.12em 0.5em 0.12em 0.3em;
      font-size: 0.85em;
      color: #fff;
      pointer-events: auto;
      min-height: 1.7em;
    }
    .pip-tile-overlay-left svg {
      width: 1.1em;
      height: 1.1em;
      margin-right: 0.1em;
    }
    .pip-tile-overlay-right {
      display: flex;
      align-items: center;
      background: rgba(20,20,20,0.72);
      border-radius: 0.35em;
      padding: 0.12em 0.4em;
      font-size: 0.85em;
      color: #fff;
      pointer-events: auto;
      min-height: 1.7em;
    }
    .pip-tile-overlay-right svg {
      width: 1.1em;
      height: 1.1em;
    }
    .pip-tile-avatar-center {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 1;
      pointer-events: none;
    }
    .pip-tile-avatar {
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 50%;
      color: #fff;
      font-weight: 600;
      font-size: 8vmin;
      background: #7C4DFF;
      box-shadow: 0 2px 12px rgba(0,0,0,0.12);
      user-select: none;
      pointer-events: auto;
      transition: width 0.2s, height 0.2s, font-size 0.2s;
      aspect-ratio: 1 / 1;
      width: 25vmin;
      height: 25vmin;
    }

    .pip-tile-avatar-small {
      font-size: 4vmin !important;
      width: 12vmin !important;
      height: 12vmin !important;
    }

    /* Responsive media queries for different PiP sizes */
    @media (max-width: 400px) {
      .pip-tile-overlay-left,
      .pip-tile-overlay-right {
        font-size: 4vmin;
        padding: 0.8vmin 1.5vmin;
        border-radius: 1.2vmin;
      }

      .pip-tile-overlay-left svg,
      .pip-tile-overlay-right svg {
        width: 5vmin;
        height: 5vmin;
      }

      .pip-tile-avatar {
        font-size: 6vmin;
        width: 20vmin;
        height: 20vmin;
      }
    }

    @media (max-width: 300px) {
      .pip-tile-overlay-left,
      .pip-tile-overlay-right {
        font-size: 3.5vmin;
        padding: 0.6vmin 1.2vmin;
        border-radius: 1vmin;
      }

      .pip-tile-overlay-left svg,
      .pip-tile-overlay-right svg {
        width: 4.5vmin;
        height: 4.5vmin;
      }

      .pip-tile-avatar {
        font-size: 5.5vmin;
        width: 18vmin;
        height: 18vmin;
      }
    }

    @media (max-width: 200px) {
      .pip-tile-overlay-left,
      .pip-tile-overlay-right {
        font-size: 3vmin;
        padding: 0.5vmin 1vmin;
        border-radius: 0.8vmin;
        gap: 0.5vmin;
      }

      .pip-tile-overlay-left svg,
      .pip-tile-overlay-right svg {
        width: 4vmin;
        height: 4vmin;
      }

      .pip-tile-avatar {
        font-size: 5vmin;
        width: 15vmin;
        height: 15vmin;
      }
    }

    @media (max-width: 100px) {
      .pip-tile-overlay-left,
      .pip-tile-overlay-right {
        font-size: 2.5vmin;
        padding: 0.4vmin 0.8vmin;
        border-radius: 0.6vmin;
      }

      .pip-tile-overlay-left svg,
      .pip-tile-overlay-right svg {
        width: 3vmin;
        height: 3vmin;
      }

      .pip-tile-avatar {
        font-size: 4.5vmin;
        width: 12vmin;
        height: 12vmin;
      }
    }

    /* Height-based media queries */
    @media (max-height: 200px) {
      .pip-tile-overlay-left,
      .pip-tile-overlay-right {
        font-size: 3vmin;
        padding: 0.5vmin 1vmin;
      }

      .pip-tile-avatar {
        font-size: 5vmin;
        width: 15vmin;
        height: 15vmin;
      }
    }

    @media (max-height: 100px) {
      .pip-tile-overlay-left,
      .pip-tile-overlay-right {
        font-size: 2.5vmin;
        padding: 0.4vmin 0.8vmin;
      }

      .pip-tile-avatar {
        font-size: 4.5vmin;
        width: 12vmin;
        height: 12vmin;
      }
    }

    /* Corner pulse animation for speaking indicators */
    @keyframes pip-corner-pulse {
      0% {
        opacity: 0.6;
        transform: scale(0.8);
      }
      50% {
        opacity: 1;
        transform: scale(1.2);
      }
      100% {
        opacity: 0.6;
        transform: scale(0.8);
      }
    }

    .pip-control-dot.screen-share {
      background-color: #1e8cfa;
      box-shadow: 0 4px 12px rgba(30, 140, 250, 0.3);
      border-color: rgba(255, 255, 255, 0.2);
    }

    .pip-control-dot.screen-share:hover {
      background-color: #1677ff;
      box-shadow: 0 6px 16px rgba(30, 140, 250, 0.4);
    }

    .pip-control-dot.screen-share:active {
      background-color: #0958d9;
      box-shadow: 0 2px 8px rgba(30, 140, 250, 0.3);
    }

    .pip-control-dot.screen-share svg {
      color: #fff;
    }

    /* Floating Screen Capture Button Styles */
    .pip-screen-capture-button {
      position: absolute;
      top: 1vmin;
      right: 1vmin;
      z-index: 10;
      opacity: 0.8;
      transition: opacity 0.3s ease;
    }

    .pip-screen-capture-button:hover {
      opacity: 1;
    }

    /* Override ScreenCaptureButton styles for PiP */
    .pip-screen-capture-button .settings-menu-item {
      background: rgba(0, 0, 0, 0.7);
      border-radius: 1.5vmin;
      padding: 1vmin;
      cursor: pointer;
      transition: all 0.3s ease;
      border: 1px solid rgba(255, 255, 255, 0.2);
      backdrop-filter: blur(10px);
      min-width: auto;
      width: auto;
      height: auto;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .pip-screen-capture-button .settings-menu-item:hover {
      background: rgba(0, 0, 0, 0.9);
      border-color: rgba(255, 255, 255, 0.4);
      transform: scale(1.05);
    }

    .pip-screen-capture-button .settings-menu-inner-icon {
      margin: 0;
      padding: 0;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .pip-screen-capture-button .settings-menu-inner-icon svg {
      width: 3vmin;
      height: 3vmin;
      color: #fff;
    }

    .pip-screen-capture-button .settings-menu-inner-text {
      display: none; /* Hide text in PiP to keep button small */
    }

    /* Responsive sizing for smaller PiP windows */
    @media (max-width: 400px) {
      .pip-screen-capture-button {
        top: 2vmin;
        right: 2vmin;
      }

      .pip-screen-capture-button .settings-menu-inner-icon svg {
        width: 4vmin;
        height: 4vmin;
      }
    }

    @media (max-width: 300px) {
      .pip-screen-capture-button .settings-menu-inner-icon svg {
        width: 5vmin;
        height: 5vmin;
      }
    }

  `, []);

  // Simple error handling
  const handlePipError = useCallback(() => {
    setToastNotification("Failed to open Picture-in-Picture");
    setToastStatus("error");
    setShowToast(true);
  }, [setToastNotification, setToastStatus, setShowToast]);

  // Simple PiP window opening
  const openPipWindow = useCallback(async () => {
    if (!isSupported) {
      handlePipError(new Error('Document Picture-in-Picture not supported'));
      return false;
    }

    if (pipWindowRef.current) {
      return true;
    }

    try {
      const pipWindow = await window.documentPictureInPicture.requestWindow({
        width: defaultConfig.width,
        height: defaultConfig.height,
      });

      pipWindowRef.current = pipWindow;
      setPipWindowDocument(pipWindow.document);
      setIsPIPEnabled(true);

      // Setup document
      const pipDoc = pipWindow.document;
      const style = pipDoc.createElement('style');
      style.textContent = getPipStyles();
      pipDoc.head.appendChild(style);

      const container = pipDoc.createElement('div');
      container.id = 'pip-root';
      pipDoc.body.appendChild(container);
      pipContainerRef.current = container;

      // Simple close handler
      pipWindow.addEventListener('pagehide', () => {
        closePipWindow();
      });

      return true;
    } catch (error) {
      handlePipError(error);
      return false;
    }
  }, [isSupported, defaultConfig, getPipStyles, setIsPIPEnabled, closePipWindow, handlePipError]);

  // Toggle PiP mode
  const togglePipMode = useCallback(async (enabled) => {
    if (enabled) {
      return openPipWindow();
    } else {
      closePipWindow();
      return true;
    }
  }, [openPipWindow, closePipWindow]);

  // Simple PiP content rendering
  const pipPortal = useMemo(() => {
    if (!pipWindowDocument || !pipContainerRef.current) return null;

    return createPortal(
      <div className="pip-container">
        <PipContent />
      </div>,
      pipContainerRef.current
    );
  }, [pipWindowDocument, PipContent]);

  return {
    togglePipMode,
    pipPortal,
    isSupported,
    controlPosition // Expose current control position
  };
}